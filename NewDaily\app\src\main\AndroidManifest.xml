<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />

    <application
        android:allowBackup="true"
        android:icon="@mipmap/jojo_icon"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/jojo_icon_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme">
        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="ca-app-pub-2128320934897837~9551035588"/>

        <receiver
            android:name=".Restarter"
            android:enabled="true"
             />

        <service
            android:name=".AlarmService"
            android:enabled="true"
             />

        <activity
            android:name=".EnterTasks2"
            android:label="@string/title_activity_enter_tasks2" />
        <activity
            android:name=".HowManyTasks"
            android:label="@string/title_activity_how_many_tasks"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity android:name=".FourthAlarm" />
        <activity android:name=".SecondAlarm" />
        <activity android:name=".FirstAlarm" />
        <activity android:name=".ThirdAlarm" />
        <activity android:name=".MainActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity android:name=".TasksOrNormal" />
        <activity android:name=".Normal" />
        <activity android:name=".FinalAct" />
        <activity android:name=".hours" />

        <receiver android:name=".AlarmReciever"
            android:enabled="true"
            />
    </application>

</manifest>