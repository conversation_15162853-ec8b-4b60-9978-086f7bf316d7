package com.don.newdaily;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.Lifecycle;
import androidx.viewpager2.adapter.FragmentStateAdapter;

public class Page<PERSON>lideAdapter extends FragmentStateAdapter {


    public PageSlideAdapter(@NonNull FragmentActivity fragmentActivity) {
        super(fragmentActivity);
    }

    public PageSlideAdapter(@NonNull Fragment fragment) {
        super(fragment);
    }

    public PageSlideAdapter(@NonNull FragmentManager fragmentManager, @NonNull Lifecycle lifecycle) {
        super(fragmentManager, lifecycle);
    }

    @NonNull
    @Override
    public Fragment createFragment(int position) {
        switch (position){
            case 0:
                return new BroughtToYou();
            case 1:
                return new StartButtonPage();
            default:
                return new StartButtonPage();
        }
    }

    @Override
    public int getItemCount() {
        return 2;
    }
}
