<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/boxInsetLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorPrimaryDark"
    android:gravity="center"
    android:orientation="vertical"
    tools:context=".AlarmActivity">

    <TextView
        android:id="@+id/txtview1"
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:text="" />

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="2dp"
        android:background="?android:attr/listDivider" />

    <TextView
        android:id="@+id/txtview2"
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:text="" />

    <View
        android:id="@+id/divider2"
        android:layout_width="match_parent"
        android:layout_height="2dp"
        android:background="?android:attr/listDivider" />

    <TextView
        android:id="@+id/txtview3"
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:text="" />

    <View
        android:id="@+id/divider3"
        android:layout_width="match_parent"
        android:layout_height="2dp"
        android:background="?android:attr/listDivider" />

    <TextView
        android:id="@+id/txtview4"
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:text="" />

    <View
        android:id="@+id/divider4"
        android:layout_width="match_parent"
        android:layout_height="2dp"
        android:background="?android:attr/listDivider" />

    <TextView
        android:id="@+id/textView8"
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:text="" />

</LinearLayout>