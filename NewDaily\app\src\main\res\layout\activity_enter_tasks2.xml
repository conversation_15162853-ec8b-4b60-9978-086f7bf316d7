<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:ads="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/darkblueclouds"
    android:padding="@dimen/box_inset_layout_padding"
    tools:context=".EnterTasks2">

    <TextView
        android:id="@+id/tasksFirstTitle"
        android:layout_width="384dp"
        android:layout_height="165dp"
        android:layout_marginStart="1dp"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="8dp"
        android:gravity="center"
        android:textColor="#FFFFFF"
        android:textSize="35sp"
        android:textStyle="bold"
        ads:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="@string/enterFirstTask" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/textInputLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.textfield.TextInputEditText
        android:id="@+id/EnterTasks"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="216dp"
        android:layout_marginBottom="2dp"
        android:hint="@string/tasks"
        android:textAlignment="center"
        android:textColor="#FFFFFF"
        app:layout_constraintBottom_toTopOf="@+id/NextBtn"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <Button
        android:id="@+id/NextBtn"
        android:layout_width="263dp"
        android:layout_height="62dp"
        android:layout_marginTop="264dp"
        android:layout_marginEnd="128dp"
        android:background="#276FA8"
        android:backgroundTint="#33FFFFFF"
        android:text="@string/NextBtn"
        android:textSize="36sp"
        android:textColor="#FFFFFF"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <Button
        android:id="@+id/done"
        android:background="#276FA8"
        android:backgroundTint="#33FFFFFF"
        android:layout_width="265dp"
        android:layout_height="62dp"
        android:layout_marginTop="348dp"
        android:text="@string/done"
        android:textSize="36sp"
        android:textColor="#FFFFFF"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.gms.ads.AdView
        android:id="@+id/adView1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        ads:adSize="BANNER"
        ads:adUnitId="ca-app-pub-2128320934897837/3532422140"
        ads:layout_constraintBottom_toBottomOf="parent"
        ads:layout_constraintEnd_toEndOf="parent"
        ads:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>