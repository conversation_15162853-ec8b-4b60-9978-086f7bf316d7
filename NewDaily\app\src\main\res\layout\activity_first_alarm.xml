<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/lightblueclouds"
    tools:context=".FirstAlarm">

    <TextView
        android:id="@+id/startPageBanner"
        android:layout_width="372dp"
        android:layout_height="99dp"
        android:layout_marginBottom="484dp"
        android:gravity="center"
        android:text="@string/firstAlarm"
        android:textColor="#0611FA"
        android:textSize="24sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <Button
        android:id="@+id/AlarmStop"
        android:layout_width="168dp"
        android:layout_height="69dp"
        android:layout_marginBottom="320dp"
        android:background="#276FA8"
        android:backgroundTint="#33FFFFFF"
        android:text="@string/stopAlarm"
        android:textSize="24sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/textView2"
        android:layout_width="381dp"
        android:layout_height="48dp"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="268dp"
        android:gravity="center"
        android:text="@string/autoStartText"
        android:textColor="#F82105"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/AlarmStop" />

    <Button
        android:id="@+id/cancelAlarm"
        android:layout_width="148dp"
        android:layout_height="37dp"
        android:layout_marginBottom="144dp"
        android:background="#276FA8"
        android:backgroundTint="#33FFFFFF"
        android:text="@string/cancel"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:ignore="TouchTargetSizeCheck" />

    <TextView
        android:id="@+id/nextAct"
        android:layout_width="217dp"
        android:layout_height="54dp"
        android:layout_marginBottom="416dp"
        android:gravity="center"
        android:textColor="#0611FA"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>