<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".FourthAlarm"
    android:background="@drawable/pinkishclouds">

    <Button
        android:id="@+id/AlarmStop"
        android:layout_width="168dp"
        android:layout_height="69dp"
        android:layout_marginEnd="112dp"
        android:layout_marginBottom="316dp"
        android:background="#276FA8"
        android:backgroundTint="#33FFFFFF"
        android:text="@string/stopAlarm"
        android:textSize="24sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <TextView
        android:id="@+id/startPageBanner"
        android:layout_width="235dp"
        android:layout_height="81dp"
        android:layout_marginBottom="480dp"
        android:gravity="center"
        android:text="@string/finalAlarm"
        android:textColor="#1D3CFD"
        android:textSize="34sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/textView2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="280dp"
        android:text="@string/finalStop"
        android:textAlignment="center"
        android:textColor="#F82105"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/timesUp"
        android:layout_width="204dp"
        android:layout_height="82dp"
        android:layout_marginBottom="108dp"
        android:text="@string/thanksDaily"
        android:textAlignment="center"
        android:textColor="#090707"
        android:textSize="20sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.497"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>