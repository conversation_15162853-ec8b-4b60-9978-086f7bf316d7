<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:ads="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/lightblueclouds"

    android:orientation="vertical"
    tools:context=".HowManyTasks">

    <TextView
        android:id="@+id/howManyTasksHeader"
        android:layout_width="match_parent"
        android:layout_height="192dp"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="6dp"
        android:layout_marginEnd="0dp"
        android:gravity="center"
        android:text="@string/HowManyTasks"
        android:textAlignment="center"
        android:textColor="#0C0C0C"
        android:textSize="30sp"
        android:textStyle="bold" />

    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/howManyTasksHeader"
        android:layout_marginTop="-122dp"
        android:hint="@string/hours1"
        >

    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.textfield.TextInputEditText
        android:id="@+id/userTaskAmount"
        android:layout_width="match_parent"
        android:layout_height="46dp"
        android:layout_alignParentEnd="true"
        android:layout_alignParentBottom="true"
        android:layout_marginEnd="2dp"
        android:layout_marginBottom="464dp"
        android:background="#276FA8"
        android:backgroundTint="#33FFFFFF"
        android:hint="@string/amtOfTasks"
        android:inputType="number"
        android:textAlignment="center"
        android:textColor="#0E0D0D"
        android:textSize="20sp" />

    <Button
        android:id="@+id/hoursEnter"
        android:layout_width="283dp"
        android:layout_height="67dp"
        android:layout_alignParentStart="true"
        android:layout_alignParentEnd="true"
        android:layout_alignParentBottom="true"
        android:layout_marginStart="63dp"
        android:layout_marginEnd="65dp"
        android:layout_marginBottom="348dp"
        android:background="#276FA8"
        android:backgroundTint="#33FFFFFF"
        android:text="@string/enter"
        android:textSize="24sp"
        android:textStyle="bold" />

    <com.google.android.gms.ads.AdView
        android:id="@+id/adView1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="0dp"
        ads:adSize="BANNER"
        ads:adUnitId="ca-app-pub-2128320934897837/3532422140" />

</RelativeLayout>