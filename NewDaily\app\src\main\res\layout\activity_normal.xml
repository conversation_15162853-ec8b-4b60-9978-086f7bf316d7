<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical"
    tools:context=".Normal"
    android:background="@drawable/lightblueclouds"

    >

    <TextView
        android:id="@+id/v1"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:textColor="#0B2FF6"
        android:textSize="20sp"
        android:textStyle="bold" />

    <Space
        android:layout_width="match_parent"
        android:layout_height="50dp" />

    <TextView
        android:id="@+id/v2"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:textColor="#0B2FF6"
        android:textSize="20sp"
        android:textStyle="bold" />

    <Space
        android:layout_width="match_parent"
        android:layout_height="50dp" />

    <TextView
        android:id="@+id/v3"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:textColor="#0B2FF6"
        android:textSize="20sp"
        android:textStyle="bold" />

    <Space
        android:layout_width="match_parent"
        android:layout_height="50dp" />

    <TextView
        android:id="@+id/v4"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:textColor="#0B2FF6"
        android:textSize="20sp"
        android:textStyle="bold" />

</LinearLayout>