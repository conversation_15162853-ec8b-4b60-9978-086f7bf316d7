<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".BroughtToYou">

    <!-- TODO: Update blank fragment layout -->

    <ImageView
        android:id="@+id/imageView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:scaleType="centerCrop"
        android:scaleX="1"
        android:scaleY="1"
        android:src="@drawable/ocean_black" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="164dp"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="26dp"
        android:layout_marginEnd="0dp"
        android:fontFamily="sans-serif-condensed-light"
        android:gravity="center"
        android:text="@string/brought"
        android:textAlignment="center"
        android:textColor="#59E352"
        android:textSize="25sp"
        android:textStyle="italic" />

</RelativeLayout>