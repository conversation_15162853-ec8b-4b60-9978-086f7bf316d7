<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".SettingsFragment"
    android:background="@drawable/darkblueclouds"
    >

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/settingsHeader"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/colorPrimary"
        android:clickable="false"
        android:minHeight="?attr/actionBarSize"
        android:theme="?attr/actionBarTheme" />

    <TextView
        android:id="@+id/howtoUse"
        android:layout_width="328dp"
        android:layout_height="128dp"
        android:layout_below="@+id/settingsHeader"
        android:layout_alignParentStart="true"
        android:layout_centerHorizontal="true"
        android:layout_marginStart="31dp"
        android:layout_marginTop="76dp"
        android:background="#276FA8"
        android:backgroundTint="#33FFFFFF"
        android:gravity="center"
        android:text="@string/directions"
        android:textAlignment="center"
        android:textColor="#171714"
        android:textSize="48sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/chooseTheme"
        android:layout_width="328dp"
        android:layout_height="128dp"
        android:layout_alignParentStart="true"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginStart="31dp"
        android:layout_marginBottom="118dp"
        android:background="#276FA8"
        android:backgroundTint="#33FFFFFF"
        android:gravity="center"
        android:text="@string/themes"
        android:textAlignment="center"
        android:textColor="#171714"
        android:textSize="34sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/customPresets"
        android:layout_width="329dp"
        android:layout_height="143dp"
        android:layout_below="@+id/settingsHeader"
        android:layout_alignParentStart="true"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginStart="31dp"
        android:layout_marginTop="266dp"
        android:layout_marginBottom="320dp"
        android:background="#276FA8"
        android:backgroundTint="#33FFFFFF"
        android:gravity="center"
        android:text="@string/customAlarms"
        android:textColor="#171714"
        android:textSize="24sp"
        android:textStyle="bold" />
</RelativeLayout>
