<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/MainLay"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity"
    android:background="@drawable/lightblueclouds"
    >

    <TextView
        android:id="@+id/startPageBanner"
        android:layout_width="392dp"
        android:layout_height="420dp"
        android:layout_marginStart="20dp"
        android:layout_marginTop="30dp"
        android:layout_marginEnd="20dp"
        android:layout_marginBottom="600dp"
        android:backgroundTint="#59BA35"
        android:backgroundTintMode="add"
        android:fontFamily="sans-serif"
        android:gravity="center"
        android:text="@string/welcome"
        android:textAppearance="@style/TextAppearance.AppCompat.Large"
        android:textColor="#0C0C0C"
        android:textSize="60sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.512"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.0" />

    <Button
        android:id="@+id/startBtn"
        android:layout_width="283dp"
        android:layout_height="67dp"
        android:layout_marginStart="145dp"
        android:layout_marginTop="50dp"
        android:layout_marginEnd="145dp"
        android:layout_marginBottom="336dp"
        android:background="#276FA8"
        android:backgroundTint="#33FFFFFF"
        android:onClick="onClick"
        android:text="@string/pressToStart"
        android:textAlignment="center"
        android:textSize="34sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/startPageBanner"
        app:layout_constraintVertical_bias="0.437" />

    <ImageView
        android:id="@+id/settingsBtn"
        android:layout_width="49dp"
        android:layout_height="48dp"
        android:layout_marginEnd="172dp"
        android:layout_marginBottom="48dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:srcCompat="@drawable/ic_settings" />

</androidx.constraintlayout.widget.ConstraintLayout>