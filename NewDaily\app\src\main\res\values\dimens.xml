<resources>
    <dimen name="fab_margin">16dp</dimen>
    <!-- Default screen margins, per the Android Design guidelines. -->
    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin">16dp</dimen>
    <dimen name="appbar_padding">16dp</dimen>
    <dimen name="appbar_padding_top">8dp</dimen>
    <!--
    Because the window insets on round devices are larger than 15dp, this padding only applies
    to square screens.
    -->
    <dimen name="box_inset_layout_padding">0dp</dimen>

    <!--
    This padding applies to both square and round screens. The total padding between the buttons
    and the window insets is box_inset_layout_padding (above variable) on square screens and
    inner_frame_layout_padding (below variable) on round screens.
    -->
    <dimen name="inner_frame_layout_padding">5dp</dimen>
</resources>
