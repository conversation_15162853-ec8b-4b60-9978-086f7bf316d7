<resources>
    <string name="app_name">NewDaily</string>
    <string name="welcome">WELCOME TO DAILY PLANNER</string>
    <string name="input">INPUT YOUR ACTIVITIES</string>
    <string name="title_activity_enter_tasks">EnterTasks</string>
    <string name="task1">task 1</string>
    <string name="task2">task 2</string>
    <string name="task3">task 3</string>
    <string name="task4">task 4</string>
    <string name="proceed">CONTINUE WITH STANDARD DAY(4 ACTIVITIES)</string>
    <string name="taskTitle">ENTER WHAT TASKS YOU WISH TO COMPLETE IN DESCENDING ORDER OF IMPORTANCE:</string>
    <string name="enter">ENTER</string>
    <string name="hours">ENTER THE AMOUNT OF TIME YOU HAVE IN HOURS</string>
    <string name="hours1">ENTER HOURS..</string>
    <string name="alarmsButton">START ALARMS WHEN FINSHED</string>
    <string name="firstAlarm">MOVE ON TO YOUR SECOND ACTIVITY!</string>
    <string name="secondAlarm">MOVE ON TO YOUR THIRD ACTIVITY!</string>
    <string name="thirdAlarm">MOVE ON TO YOUR FOURTH ACTIVITY!</string>
    <string name="finalAlarm">FINAL ALARM!!!</string>
    <string name="stopAlarm">STOP ALARM</string>
    <string name="autoStartText">(Pressing STOP ALARM button automatically starts next alarm)</string>
    <string name="cancel">Cancel Alarms</string>
    <string name="NextActivity">Cancel Alarms</string>
    <string name="HowManyTasks">How many tasks do you wish to set alarms for?(Up to 4)</string>
    <string name="AlarmsStartedAuto">Alarms will be started automatically</string>
    <string name="tasks">ENTER TASK...</string>
    <string name="TaskToCompleteFirst">Enter which task you would like to complete first:</string>
    <string name="NextBtn">NEXT</string>
    <string name="done">DONE</string>
    <string name="begin">BEGIN</string>
    <string name="pressToStart">Press To Start</string>
    <string name="hoursToContinue">Please enter the amount of time you have in hours to continue</string>
    <string name="brought">Brought to you by</string>
    <string name="amtOfTasks">Press to enter amount of tasks.. </string>
    <string name="enterFirstTask">Enter The Name Of Task You Wish To Complete First</string>
    <string name="finalStop">(Pressing the stop button will stop this final alarm)</string>
    <string name="directions">How to use</string>
    <string name="themes">CHOOSE THEME (coming soon!)</string>
    <string name="customAlarms">Customize your alarm presets (coming soon!)</string>
    <string name="howToUse1">This app is meant to aid users who regularly find themselves with a short amount of time to complete various important tasks or activities on a daily basis.
</string>
    <string name="howToUse2">To effectively use this app to complete tasks, the user is asked to select tasks they wish to complete (up to four tasks may be entered) and the amount of time they have for completing them.
</string>
    <string name="howToUse3">DailyPlanner <b>is not</b> meant for attempting to complete a large amount of activities in a small amount of time. We recognize there are other alarm products who offer functionality that would be sufficient toward that end. We instead aim to provide a service for users who wish to complete some of the most meaningful activities in their day while utilizing the short time available to them to the best of their ability
</string>
    <string name="previous">Previous</string>
    <string name="thanksDaily">Times up! Thank you for using your <b>DAILY PLANNER!</b></string>


    <!-- Strings used for fragments for navigation -->
    <string name="first_fragment_label">First Fragment</string>
    <string name="second_fragment_label">Second Fragment</string>
    <string name="next">Next</string>
    <string name="disclaimer">Disclaimer</string>
    <string name="warning">Warning!</string>

    <string name="hello_first_fragment">Hello first fragment</string>
    <string name="hello_second_fragment">Hello second fragment. Arg: %1$s</string>
    <string name="title_activity_main2">Main2Activity</string>
    <string name="title_activity_tabbed">Tabbed</string>
    <string name="tab_text_1">Tab 1</string>
    <string name="tab_text_2">Tab 2</string>
    <string name="title_activity_go_herbs">GoHerbs</string>
    <!--
    This string is used for square devices and overridden by hello_world in
    values-round/strings.xml for round devices.
    -->
    <string name="hello_world">Hello Square World!</string>
    <string name="title_activity_settings">Settings</string>
    <string name="title_activity_how_many_tasks">HowManyTasks</string>
    <string name="title_activity_enter_tasks2">EnterTasks2</string>
    <string name="title_activity_alarm">AlarmActivity</string>
    <string name="title_activity_first_full">FullscreenActivity</string>
    <string name="dummy_button">Dummy Button</string>
    <string name="dummy_content">DUMMY\nCONTENT</string>
    <!-- TODO: Remove or change this placeholder text -->
    <string name="hello_blank_fragment">Hello blank fragment</string>

</resources>
