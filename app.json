{"expo": {"name": "NewDay", "slug": "NewDay", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "newday", "userInterfaceStyle": "automatic", "platforms": ["ios", "android", "web"], "newArchEnabled": false, "ios": {"supportsTablet": true, "bundleIdentifier": "com.pixelparqour.newday"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.pixelparqour.newday", "permissions": ["CAMERA", "INTERNET", "ACCESS_NETWORK_STATE"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}]], "experiments": {"typedRoutes": true}}}