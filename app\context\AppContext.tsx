import React, { createContext, useContext, useState } from 'react';

interface Task {
  name: string;
}

interface AppContextType {
  tasks: Task[];
  setTasks: React.Dispatch<React.SetStateAction<Task[]>>;
  taskAmount: number;
  setTaskAmount: React.Dispatch<React.SetStateAction<number>>;
  hours: number;
  setHours: React.Dispatch<React.SetStateAction<number>>;
  useAlarms: boolean;
  setUseAlarms: React.Dispatch<React.SetStateAction<boolean>>;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

export function AppProvider({ children }: { children: React.ReactNode }) {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [taskAmount, setTaskAmount] = useState<number>(0);
  const [hours, setHours] = useState<number>(0);
  const [useAlarms, setUseAlarms] = useState<boolean>(false);

  return (
    <AppContext.Provider
      value={{
        tasks,
        setTasks,
        taskAmount,
        setTaskAmount,
        hours,
        setHours,
        useAlarms,
        setUseAlarms,
      }}
    >
      {children}
    </AppContext.Provider>
  );
}

export function useAppContext() {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
}
