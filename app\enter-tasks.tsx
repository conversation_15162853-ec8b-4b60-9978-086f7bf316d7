import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TextInput, TouchableOpacity, KeyboardAvoidingView, Platform } from 'react-native';
import { router } from 'expo-router';
import { useAppContext } from './context/AppContext';

export default function EnterTasks() {
  const { taskAmount, setTasks } = useAppContext();
  const [currentTask, setCurrentTask] = useState('');
  const [taskList, setTaskList] = useState<string[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [headerText, setHeaderText] = useState(`Enter task 1 of ${taskAmount}`);
  const [doneBtnCounter, setDoneBtnCounter] = useState(0);

  useEffect(() => {
    if (currentIndex < taskAmount) {
      setHeaderText(`Enter task ${currentIndex + 1} of ${taskAmount}`);
    } else {
      setHeaderText('All tasks entered. Press DONE to continue.');
    }
  }, [currentIndex, taskAmount]);

  const handleNextPress = () => {
    if (currentTask.trim().length > 0) {
      const updatedList = [...taskList, currentTask];
      setTaskList(updatedList);
      setCurrentTask('');
      
      if (currentIndex + 1 < taskAmount) {
        setCurrentIndex(currentIndex + 1);
        setHeaderText(`Enter task ${currentIndex + 2} of ${taskAmount}`);
      } else {
        setCurrentIndex(currentIndex + 1);
        setHeaderText('All tasks entered. Press DONE to continue.');
      }
    } else {
      setHeaderText(`Please enter task ${currentIndex + 1}`);
    }
  };

  const handleDonePress = () => {
    if (currentIndex < taskAmount) {
      if (currentTask.trim().length >= 1) {
        setHeaderText('Press NEXT to submit your entry');
      } else {
        setHeaderText(`Please enter (${taskAmount - currentIndex}) more tasks`);
      }
      setDoneBtnCounter(0);
    } else {
      if (doneBtnCounter < 1) {
        setHeaderText('Press DONE again to confirm your entry');
        setDoneBtnCounter(doneBtnCounter + 1);
      } else {
        // Save tasks to context
        setTasks(taskList.map(task => ({ name: task })));
        router.push('/hours');
      }
    }
  };

  return (
    <KeyboardAvoidingView 
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <Text style={styles.header}>{headerText}</Text>
      
      <TextInput
        style={styles.input}
        value={currentTask}
        onChangeText={setCurrentTask}
        placeholder="Enter your task"
        maxLength={50}
      />
      
      <View style={styles.buttonContainer}>
        <TouchableOpacity 
          style={styles.button} 
          onPress={handleNextPress}
          disabled={currentIndex >= taskAmount}
        >
          <Text style={[
            styles.buttonText, 
            currentIndex >= taskAmount && styles.disabledButtonText
          ]}>
            Next
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.button} 
          onPress={handleDonePress}
        >
          <Text style={styles.buttonText}>Done</Text>
        </TouchableOpacity>
      </View>
      
      {taskList.length > 0 && (
        <View style={styles.taskListContainer}>
          <Text style={styles.taskListHeader}>Tasks entered:</Text>
          {taskList.map((task, index) => (
            <Text key={index} style={styles.taskItem}>
              {index + 1}. {task}
            </Text>
          ))}
        </View>
      )}
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#e6f2ff',
    padding: 20,
    alignItems: 'center',
  },
  header: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#276FA8',
    marginTop: 20,
    marginBottom: 30,
    textAlign: 'center',
  },
  input: {
    width: '90%',
    height: 60,
    backgroundColor: 'white',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#276FA8',
    padding: 15,
    fontSize: 18,
    marginBottom: 30,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    marginBottom: 30,
  },
  button: {
    backgroundColor: 'rgba(39, 111, 168, 0.2)',
    padding: 15,
    borderRadius: 10,
    width: '40%',
    alignItems: 'center',
  },
  buttonText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#276FA8',
  },
  disabledButtonText: {
    color: '#9DBAD7',
  },
  taskListContainer: {
    width: '90%',
    padding: 15,
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    borderRadius: 10,
  },
  taskListHeader: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#276FA8',
  },
  taskItem: {
    fontSize: 16,
    marginBottom: 5,
    color: '#333',
  },
});
