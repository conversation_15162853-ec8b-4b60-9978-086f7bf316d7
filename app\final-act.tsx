import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView, Alert } from 'react-native';
import { useAppContext } from './context/AppContext';

// Helper function to calculate time for each task
const calculateTaskTime = (totalHours: number, taskCount: number, taskIndex: number) => {
  // Distribute time based on task importance (first task gets more time)
  const weights = [0.4, 0.3, 0.2, 0.1]; // 40%, 30%, 20%, 10%
  const totalMinutes = totalHours * 60;
  
  // If we have fewer than 4 tasks, redistribute the weights
  const adjustedWeights = weights.slice(0, taskCount);
  const weightSum = adjustedWeights.reduce((sum, weight) => sum + weight, 0);
  const normalizedWeights = adjustedWeights.map(weight => weight / weightSum);
  
  const minutes = Math.floor(totalMinutes * normalizedWeights[taskIndex]);
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  
  return { hours, minutes: remainingMinutes };
};

export default function FinalAct() {
  const { tasks, taskAmount, hours, useAlarms } = useAppContext();
  const [taskTimes, setTaskTimes] = useState<{
    name: string;
    hours: number;
    minutes: number;
  }[]>([]);

  useEffect(() => {
    // Calculate time for each task
    const calculatedTasks = tasks.slice(0, taskAmount).map((task, index) => ({
      name: task.name,
      ...calculateTaskTime(hours, taskAmount, index),
    }));
    
    setTaskTimes(calculatedTasks);
    
    // Show alarm notification if alarms are enabled
    if (useAlarms && calculatedTasks.length > 0) {
      const firstTask = calculatedTasks[0];
      const alarmTime = new Date();
      alarmTime.setHours(alarmTime.getHours() + firstTask.hours);
      alarmTime.setMinutes(alarmTime.getMinutes() + firstTask.minutes);
      
      const formattedTime = alarmTime.toLocaleTimeString([], { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
      
      Alert.alert(
        'Alarm Set',
        `First alarm set for ${formattedTime}`,
        [{ text: 'OK' }]
      );
    }
  }, [tasks, taskAmount, hours, useAlarms]);

  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.header}>Your Task Schedule</Text>
        
        {taskTimes.map((task, index) => (
          <View key={index} style={styles.taskCard}>
            <Text style={styles.taskName}>{task.name}</Text>
            <Text style={styles.taskTime}>
              Time allocated: {task.hours} hours and {task.minutes} minutes
            </Text>
          </View>
        ))}
        
        {useAlarms && (
          <Text style={styles.alarmsNote}>
            Alarms have been set based on your task schedule
          </Text>
        )}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#e6f2ff',
  },
  content: {
    padding: 20,
    alignItems: 'center',
  },
  header: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#276FA8',
    marginVertical: 20,
    textAlign: 'center',
  },
  taskCard: {
    width: '100%',
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  taskName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#276FA8',
    marginBottom: 10,
  },
  taskTime: {
    fontSize: 18,
    color: '#333',
  },
  alarmsNote: {
    marginTop: 20,
    fontSize: 16,
    color: '#B85252',
    textAlign: 'center',
  },
});
