import React, { useState } from 'react';
import { View, Text, StyleSheet, TextInput, TouchableOpacity, KeyboardAvoidingView, Platform } from 'react-native';
import { router } from 'expo-router';
import { useAppContext } from './context/AppContext';

export default function Hours() {
  const { setHours, useAlarms } = useAppContext();
  const [hoursInput, setHoursInput] = useState('');
  const [headerText, setHeaderText] = useState('How many hours do you have available today?');

  const handleEnterPress = () => {
    if (hoursInput.trim().length > 0) {
      const numHours = parseInt(hoursInput);
      if (!isNaN(numHours) && numHours > 0) {
        setHours(numHours);
        router.push('/final-act');
      } else {
        setHeaderText('Please enter a valid number of hours');
      }
    } else {
      setHeaderText('Please enter the number of hours you have available today');
    }
  };

  return (
    <KeyboardAvoidingView 
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <Text style={styles.header}>{headerText}</Text>
      
      <TextInput
        style={styles.input}
        keyboardType="numeric"
        value={hoursInput}
        onChangeText={setHoursInput}
        placeholder="Enter available hours"
        maxLength={2}
      />
      
      <TouchableOpacity 
        style={styles.button} 
        onPress={handleEnterPress}
      >
        <Text style={styles.buttonText}>Enter</Text>
      </TouchableOpacity>
      
      {useAlarms && (
        <Text style={styles.alarmsNote}>
          Note: Alarms will be set based on your schedule
        </Text>
      )}
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#e6f2ff',
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  header: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#276FA8',
    marginBottom: 30,
    textAlign: 'center',
  },
  input: {
    width: '80%',
    height: 60,
    backgroundColor: 'white',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#276FA8',
    padding: 15,
    fontSize: 20,
    marginBottom: 30,
    textAlign: 'center',
  },
  button: {
    backgroundColor: 'rgba(39, 111, 168, 0.2)',
    padding: 15,
    borderRadius: 10,
    width: '50%',
    alignItems: 'center',
  },
  buttonText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#276FA8',
  },
  alarmsNote: {
    marginTop: 30,
    fontSize: 16,
    color: '#B85252',
    textAlign: 'center',
  },
});
