import React, { useState } from 'react';
import { View, Text, StyleSheet, TextInput, TouchableOpacity, KeyboardAvoidingView, Platform } from 'react-native';
import { router } from 'expo-router';
import { useAppContext } from './context/AppContext';

export default function HowManyTasks() {
  const { setTaskAmount } = useAppContext();
  const [taskInput, setTaskInput] = useState('');
  const [headerText, setHeaderText] = useState('How many tasks would you like to complete today?');

  const handleEnterPress = () => {
    if (taskInput.trim().length > 0) {
      const numTasks = parseInt(taskInput);
      if (!isNaN(numTasks) && numTasks > 0 && numTasks <= 4) {
        setTaskAmount(numTasks);
        router.push('/enter-tasks');
      } else {
        setHeaderText('Please enter a number between 1 and 4');
      }
    } else {
      setHeaderText('Please enter the amount of tasks you wish to complete in descending order of importance (up to four tasks)');
    }
  };

  return (
    <KeyboardAvoidingView 
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <Text style={styles.header}>{headerText}</Text>
      
      <TextInput
        style={styles.input}
        keyboardType="numeric"
        value={taskInput}
        onChangeText={setTaskInput}
        placeholder="Enter number of tasks (1-4)"
        maxLength={1}
      />
      
      <TouchableOpacity 
        style={styles.button} 
        onPress={handleEnterPress}
      >
        <Text style={styles.buttonText}>Enter</Text>
      </TouchableOpacity>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#e6f2ff',
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  header: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#276FA8',
    marginBottom: 30,
    textAlign: 'center',
  },
  input: {
    width: '80%',
    height: 60,
    backgroundColor: 'white',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#276FA8',
    padding: 15,
    fontSize: 20,
    marginBottom: 30,
    textAlign: 'center',
  },
  button: {
    backgroundColor: 'rgba(39, 111, 168, 0.2)',
    padding: 15,
    borderRadius: 10,
    width: '50%',
    alignItems: 'center',
  },
  buttonText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#276FA8',
  },
});
