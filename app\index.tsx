import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';

// BroughtToYou component (splash screen)
const BroughtToYou = () => {
  return (
    <View style={styles.splashContainer}>
      <Text style={styles.splashText}>Brought to you by</Text>
      <Text style={styles.splashName}>NewDaily</Text>
    </View>
  );
};

// StartButtonPage component
const StartButtonPage = ({ onPress }: { onPress: () => void }) => {
  return (
    <View style={styles.startContainer}>
      <Text style={styles.startBanner}>NewDaily</Text>
      <TouchableOpacity style={styles.startButton} onPress={onPress}>
        <Text style={styles.startButtonText}>Press to Start</Text>
      </TouchableOpacity>
      <TouchableOpacity 
        style={styles.settingsButton}
        onPress={() => router.push('/settings')}
      >
        <Text style={styles.settingsButtonText}>⚙️</Text>
      </TouchableOpacity>
    </View>
  );
};

export default function HomeScreen() {
  const [showSplash, setShowSplash] = useState(true);

  useEffect(() => {
    // Show splash screen for 5 seconds then switch to start button page
    const timer = setTimeout(() => {
      setShowSplash(false);
    }, 5000);

    return () => clearTimeout(timer);
  }, []);

  const handleStartPress = () => {
    router.push('/tasks-or-normal');
  };

  return (
    <View style={styles.container}>
      <StatusBar style="auto" />
      {showSplash ? (
        <BroughtToYou />
      ) : (
        <StartButtonPage onPress={handleStartPress} />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#e6f2ff', // Light blue background similar to clouds
  },
  splashContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#e6f2ff',
  },
  splashText: {
    fontSize: 24,
    marginBottom: 10,
  },
  splashName: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#276FA8',
  },
  startContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#e6f2ff',
  },
  startBanner: {
    fontSize: 42,
    fontWeight: 'bold',
    color: '#276FA8',
    marginBottom: 50,
  },
  startButton: {
    width: 280,
    height: 70,
    backgroundColor: 'rgba(39, 111, 168, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
  },
  startButtonText: {
    fontSize: 34,
    fontWeight: 'bold',
    color: '#276FA8',
  },
  settingsButton: {
    position: 'absolute',
    top: 50,
    right: 20,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  settingsButtonText: {
    fontSize: 30,
  },
});
