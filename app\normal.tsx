import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView, Alert } from 'react-native';
import { useAppContext } from './context/AppContext';

// Simple time calculation helper
const calculateTime = (totalHours: number, category: number) => {
  // Distribute hours based on category importance
  const weights = [0.4, 0.3, 0.2, 0.1]; // 40%, 30%, 20%, 10%
  const totalMinutes = totalHours * 60;
  
  const minutes = Math.floor(totalMinutes * weights[category - 1]);
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  
  return { hours, minutes: remainingMinutes };
};

export default function Normal() {
  const { hours, useAlarms } = useAppContext();
  const [timeCategories, setTimeCategories] = useState<{
    name: string;
    hours: number;
    minutes: number;
  }[]>([]);

  useEffect(() => {
    // Calculate time for each category
    const categories = [
      { name: 'WORK', ...calculateTime(hours, 1) },
      { name: 'ART', ...calculateTime(hours, 2) },
      { name: 'LEISURE', ...calculateTime(hours, 3) },
      { name: 'DAILY PLANNING', ...calculateTime(hours, 4) },
    ];
    
    setTimeCategories(categories);
    
    // Show alarm notification if alarms are enabled
    if (useAlarms) {
      const firstCategory = categories[0];
      const alarmTime = new Date();
      alarmTime.setHours(alarmTime.getHours() + firstCategory.hours);
      alarmTime.setMinutes(alarmTime.getMinutes() + firstCategory.minutes);
      
      const formattedTime = alarmTime.toLocaleTimeString([], { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
      
      Alert.alert(
        'Alarm Set',
        `First alarm set for ${formattedTime}`,
        [{ text: 'OK' }]
      );
    }
  }, [hours, useAlarms]);

  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.header}>Your Daily Schedule</Text>
        
        {timeCategories.map((category, index) => (
          <View key={index} style={styles.categoryCard}>
            <Text style={styles.categoryText}>
              Time you have for {category.name} is {category.hours} hours and {category.minutes} minutes
            </Text>
          </View>
        ))}
        
        {useAlarms && (
          <Text style={styles.alarmsNote}>
            Alarms have been set based on your schedule
          </Text>
        )}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#e6f2ff',
  },
  content: {
    padding: 20,
    alignItems: 'center',
  },
  header: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#276FA8',
    marginVertical: 20,
    textAlign: 'center',
  },
  categoryCard: {
    width: '100%',
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  categoryText: {
    fontSize: 18,
    color: '#333',
    textAlign: 'center',
  },
  alarmsNote: {
    marginTop: 20,
    fontSize: 16,
    color: '#B85252',
    textAlign: 'center',
  },
});
