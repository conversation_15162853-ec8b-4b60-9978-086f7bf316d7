import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { router } from 'expo-router';

// HowToUse component
const HowToUse = ({ onBack }: { onBack: () => void }) => {
  return (
    <View style={styles.container}>
      <TouchableOpacity style={styles.backButton} onPress={onBack}>
        <Text style={styles.backButtonText}>← Back</Text>
      </TouchableOpacity>
      
      <Text style={styles.header}>How to Use NewDaily</Text>
      
      <ScrollView style={styles.scrollContainer}>
        <Text style={styles.sectionTitle}>Getting Started</Text>
        <Text style={styles.instructionText}>
          NewDaily helps you organize your day by allocating time for tasks or general activities.
        </Text>
        
        <Text style={styles.sectionTitle}>Task Mode</Text>
        <Text style={styles.instructionText}>
          1. Choose "Input Tasks" on the mode selection screen.{'\n'}
          2. Enter the number of tasks you want to complete (1-4).{'\n'}
          3. Enter each task in order of importance.{'\n'}
          4. Enter the total hours you have available.{'\n'}
          5. View your personalized task schedule.
        </Text>
        
        <Text style={styles.sectionTitle}>Normal Mode</Text>
        <Text style={styles.instructionText}>
          1. Choose "Normal Mode" on the mode selection screen.{'\n'}
          2. Your time will be automatically allocated to four categories:{'\n'}
          • Work (40%){'\n'}
          • Art (30%){'\n'}
          • Leisure (20%){'\n'}
          • Daily Planning (10%)
        </Text>
        
        <Text style={styles.sectionTitle}>Alarms</Text>
        <Text style={styles.instructionText}>
          Toggle the "Set Alarms" switch to receive notifications when it's time to switch tasks or activities.
        </Text>
      </ScrollView>
    </View>
  );
};

export default function Settings() {
  const [showHowTo, setShowHowTo] = useState(false);
  
  if (showHowTo) {
    return <HowToUse onBack={() => setShowHowTo(false)} />;
  }
  
  return (
    <View style={styles.container}>
      <Text style={styles.header}>Settings</Text>
      
      <TouchableOpacity 
        style={styles.settingButton}
        onPress={() => setShowHowTo(true)}
      >
        <Text style={styles.settingButtonText}>How to Use</Text>
      </TouchableOpacity>
      
      <TouchableOpacity 
        style={styles.settingButton}
        onPress={() => {/* Placeholder for future functionality */}}
      >
        <Text style={styles.settingButtonText}>Presets</Text>
      </TouchableOpacity>
      
      <TouchableOpacity 
        style={styles.settingButton}
        onPress={() => {/* Placeholder for future functionality */}}
      >
        <Text style={styles.settingButtonText}>Themes</Text>
      </TouchableOpacity>
      
      <TouchableOpacity 
        style={styles.homeButton}
        onPress={() => router.push('/')}
      >
        <Text style={styles.homeButtonText}>Return to Home</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#e6f2ff',
    padding: 20,
  },
  header: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#276FA8',
    marginVertical: 20,
    textAlign: 'center',
  },
  settingButton: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  settingButtonText: {
    fontSize: 20,
    color: '#276FA8',
    fontWeight: '500',
  },
  homeButton: {
    backgroundColor: 'rgba(39, 111, 168, 0.2)',
    padding: 15,
    borderRadius: 10,
    marginTop: 20,
    alignItems: 'center',
  },
  homeButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#276FA8',
  },
  backButton: {
    alignSelf: 'flex-start',
    padding: 10,
  },
  backButtonText: {
    fontSize: 18,
    color: '#276FA8',
    fontWeight: 'bold',
  },
  scrollContainer: {
    flex: 1,
    marginTop: 10,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#276FA8',
    marginTop: 20,
    marginBottom: 10,
  },
  instructionText: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
  },
});
