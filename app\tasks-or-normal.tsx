import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Switch } from 'react-native';
import { router } from 'expo-router';
import { useAppContext } from './context/AppContext';

export default function TasksOrNormal() {
  const { setUseAlarms } = useAppContext();
  const [alarmsEnabled, setAlarmsEnabled] = useState(false);

  const handleInputPress = () => {
    setUseAlarms(alarmsEnabled);
    router.push('/how-many-tasks');
  };

  const handleProceedPress = () => {
    setUseAlarms(alarmsEnabled);
    router.push('/normal');
  };

  const toggleAlarms = () => {
    setAlarmsEnabled(previousState => !previousState);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.header}>Choose Your Mode</Text>
      
      <View style={styles.buttonContainer}>
        <TouchableOpacity 
          style={styles.button} 
          onPress={handleInputPress}
        >
          <Text style={styles.buttonText}>Input Tasks</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.button} 
          onPress={handleProceedPress}
        >
          <Text style={styles.buttonText}>Normal Mode</Text>
        </TouchableOpacity>
      </View>
      
      <View style={styles.alarmContainer}>
        <Text style={styles.alarmText}>Set Alarms</Text>
        <Switch
          trackColor={{ false: "#767577", true: "#81b0ff" }}
          thumbColor={alarmsEnabled ? "#276FA8" : "#f4f3f4"}
          ios_backgroundColor="#3e3e3e"
          onValueChange={toggleAlarms}
          value={alarmsEnabled}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#e6f2ff',
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  header: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#276FA8',
    marginBottom: 50,
    textAlign: 'center',
  },
  buttonContainer: {
    width: '100%',
    marginBottom: 50,
  },
  button: {
    backgroundColor: 'rgba(39, 111, 168, 0.2)',
    padding: 20,
    borderRadius: 10,
    marginBottom: 20,
    alignItems: 'center',
  },
  buttonText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#276FA8',
  },
  alarmContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  alarmText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#B85252',
    marginRight: 10,
  },
});
