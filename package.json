{"name": "newday", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "expo lint"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-navigation/native": "^7.1.6", "expo": "~51.0.28", "expo-blur": "~13.0.2", "expo-constants": "~16.0.2", "expo-font": "~12.0.9", "expo-haptics": "~13.0.1", "expo-image": "~1.12.15", "expo-linking": "~6.3.1", "expo-router": "~3.5.23", "expo-splash-screen": "~0.27.5", "expo-status-bar": "~1.12.1", "expo-symbols": "~0.1.1", "expo-system-ui": "~3.0.7", "expo-web-browser": "~13.0.3", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "0.74.5", "react-native-gesture-handler": "~2.16.1", "react-native-reanimated": "~3.10.1", "react-native-safe-area-context": "4.10.5", "react-native-screens": "3.31.1", "react-native-web": "~0.19.10", "react-native-webview": "13.8.6"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~18.2.79", "typescript": "~5.8.3", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0"}, "private": true}