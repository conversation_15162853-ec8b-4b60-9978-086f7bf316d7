{"name": "newday", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "expo lint"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-navigation/native": "^7.1.6", "expo": "~52.0.0", "expo-blur": "~14.0.1", "expo-constants": "~17.0.8", "expo-font": "~12.0.10", "expo-haptics": "~13.0.1", "expo-image": "~1.12.15", "expo-linking": "~6.3.1", "expo-router": "~4.0.9", "expo-splash-screen": "~0.29.13", "expo-status-bar": "~2.0.0", "expo-symbols": "~0.2.0", "expo-system-ui": "~4.0.4", "expo-web-browser": "~13.0.3", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "0.76.3", "react-native-gesture-handler": "~2.20.2", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.0.0", "react-native-web": "~0.19.13", "react-native-webview": "13.12.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~18.2.79", "typescript": "~5.8.3", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0"}, "private": true}